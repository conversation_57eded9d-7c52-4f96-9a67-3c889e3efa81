using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using NAVI.Controls;
using NAVI.Models;
using NAVI.Services;
using NAVI.Services.DAL;
using NAVI.Windows;
using NAVI.Utils;
using System.Data.SQLite;

namespace NAVI
{
    /// <summary>
    /// NationalDataControl.xaml 的交互逻辑
    /// 国保联基础导入数据源
    /// </summary>
    public partial class NationalDataControl : UserControl
    {
        private ObservableCollection<NationalData> _nationalDataList;
        private List<NationalData> _allData;
        private List<string> _columnNames;
        private DatabaseManager _databaseManager;
        private KokuhoRenRepository _kokuhoRenRepository;
        private System.Windows.Threading.DispatcherTimer _searchTimer;

        // 分页相关
        private int _currentPage = 1;
        private int _pageSize = 10;
        private int _totalRecords = 0;

        public NationalDataControl()
        {
            InitializeComponent();
            InitializeData();
            SetupEventHandlers();
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            try
            {
                // 初始化数据库管理器
                _databaseManager = new DatabaseManager();
                _kokuhoRenRepository = _databaseManager.KokuhoRenData;

                // 从数据库加载数据
                LoadDataFromDatabase();

                // 设置搜索框的占位符效果
                SetupSearchBoxPlaceholder();

                // 初始化分页
                UpdatePagination();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"データ初期化に失敗しました：{ex.Message}", "エラー",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 从数据库加载数据
        /// </summary>
        private void LoadDataFromDatabase()
        {
            try
            {
                // 设置列名（隐藏No列作为主键）
                /*_columnNames = new List<string>
                {
                    "サービス提供年月", "事業者コード", "受給者番号", "支給決定障害者氏名", "支給決定に係る障害児氏名",
                    "障害支援区分", "サービスコード", "サービス名称", "算定時間", "回数", "利用日数", "単位数", "単価",
                    "請求額", "実費算定額", "利用者負担額", "給付費", "地域生活支援事業費", "補足給付費", "特定障害者特別給付費",
                    "精神通院医療費", "療養介護医療費", "基準該当療養介護医療費", "補装具費", "高額障害福祉サービス等給付費",
                    "高額障害児通所給付費", "肢体不自由児通所医療費", "障害児入所医療費", "status"
                };*/
                _columnNames = new List<string>
                {
                    "サービス提供年月", "請求年月日", "請求回数", "審査年月", "事業者コード", "事業者名称",
                    "受給者番号", "受給者名称", "受給者名称カナ", "児童名称", "児童名称カナ",
                    "身体", "知的", "精神", "難病", "単価障害程度区分", "障害支援区分",
                    "サービスコード", "サービス名称", "算定時間", "回数", "算定時間x回数",
                    "単位数", "サービス単位", "連合会審査区分名称", "審査区分名称", "返戻事由名称",
                    "判定フラグ", "status"
                };

                // 创建动态列
                CreateDynamicColumns();

                // 使用分页查询获取数据
                Task.Run(async () => await LoadPagedDataAsync());
            }
            catch (Exception ex)
            {
                throw new Exception($"从数据库加载数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 加载分页数据
        /// </summary>
        private async Task LoadPagedDataAsync()
        {
            try
            {
                string searchText = null;

                Dispatcher.Invoke(() =>
                {
                    searchText = SearchTextBox?.Text?.Trim();
                });// SearchTextBox?.Text?.Trim();
                string whereClause = "";
                var parameters = new List<SQLiteParameter>();

                // 构建搜索条件
                if (!string.IsNullOrEmpty(searchText) && searchText != "输入保险者番号・被保险者番号・氏名・其他关键字")
                {
                    whereClause = @"受給者番号 LIKE @search OR 事業者コード LIKE @search OR 支給決定障害者氏名 LIKE @search OR
                                   支給決定に係る障害児氏名 LIKE @search OR サービス名称 LIKE @search";
                    parameters.Add(new SQLiteParameter("@search", $"%{searchText}%"));
                }

                // 使用分页查询
                var (kokuhoRenData, totalCount) = await _kokuhoRenRepository.GetPagedAsync(
                    _currentPage, _pageSize, whereClause, parameters.ToArray());

                _totalRecords = totalCount;

                // 转换数据
                var nationalDataList = ConvertKokuhoRenDataToNationalData(kokuhoRenData);
                _nationalDataList = new ObservableCollection<NationalData>(nationalDataList);

                // 更新UI（需要在UI线程中执行）
                Application.Current.Dispatcher.Invoke(() =>
                {
                    NationalDataGrid.ItemsSource = _nationalDataList;
                    UpdatePagination();
                    UpdateStatusInfo();
                });
            }
            catch (Exception ex)
            {
                throw new Exception($"加载分页数据失败：{ex.Message}");
            }
        }


        /// <summary>
        /// 将KokuhoRenData转换为NationalData
        /// </summary>
        private List<NationalData> ConvertKokuhoRenDataToNationalData(List<KokuhoRenData> kokuhoRenDataList)
        {
            var nationalDataList = new List<NationalData>();

            foreach (var data in kokuhoRenDataList)
            {
                var nationalData = new NationalData();
                nationalData.SetProperty("No", data.No);
                nationalData.SetProperty("サービス提供年月", data.サービス提供年月);
                nationalData.SetProperty("請求年月日", data.請求年月日);
                nationalData.SetProperty("請求回数", data.請求回数);
                nationalData.SetProperty("審査年月", data.審査年月);
                nationalData.SetProperty("事業者コード", data.事業者コード);
                nationalData.SetProperty("事業者名称", data.事業者名称);
                nationalData.SetProperty("受給者番号", data.受給者番号);
                nationalData.SetProperty("受給者名称", data.受給者名称);
                nationalData.SetProperty("受給者名称カナ", data.受給者名称カナ);
                nationalData.SetProperty("児童名称", data.児童名称);
                nationalData.SetProperty("児童名称カナ", data.児童名称カナ);
                nationalData.SetProperty("身体", data.身体);
                nationalData.SetProperty("知的", data.知的);
                nationalData.SetProperty("精神", data.精神);
                nationalData.SetProperty("難病", data.難病);
                nationalData.SetProperty("単価障害程度区分", data.単価障害程度区分);
                nationalData.SetProperty("障害支援区分", data.障害支援区分);
                nationalData.SetProperty("サービスコード", data.サービスコード);
                nationalData.SetProperty("サービス名称", data.サービス名称);
                nationalData.SetProperty("算定時間", data.算定時間);
                nationalData.SetProperty("回数", data.回数);
                nationalData.SetProperty("算定時間x回数", data.算定時間x回数);
                nationalData.SetProperty("単位数", data.単位数);
                nationalData.SetProperty("サービス単位", data.サービス単位);
                nationalData.SetProperty("連合会審査区分名称", data.連合会審査区分名称);
                nationalData.SetProperty("審査区分名称", data.審査区分名称);
                nationalData.SetProperty("返戻事由名称", data.返戻事由名称);
                nationalData.SetProperty("判定フラグ", data.判定フラグ);
                nationalData.SetProperty("status", data.status);

                nationalDataList.Add(nationalData);
            }

            return nationalDataList;
        }

        /// <summary>
        /// 创建动态列
        /// </summary>
        private void CreateDynamicColumns()
        {
            // 清除除操作列外的所有列
            var operationColumn = NationalDataGrid.Columns.FirstOrDefault();
            NationalDataGrid.Columns.Clear();
            if (operationColumn != null)
            {
                NationalDataGrid.Columns.Add(operationColumn);
            }

            // 添加动态列
            foreach (var columnName in _columnNames)
            {
                var column = new DataGridTextColumn
                {
                    Header = columnName,
                    Binding = new System.Windows.Data.Binding(columnName),
                    Width = GetColumnWidth(columnName)
                };
                NationalDataGrid.Columns.Add(column);
            }
        }

        /// <summary>
        /// 获取列宽度
        /// </summary>
        private DataGridLength GetColumnWidth(string columnName)
        {
            // 根据列名设置合适的宽度
            if (columnName.Contains("序号") || columnName.Contains("番号"))
                return new DataGridLength(80);
            else if (columnName.Contains("氏名") || columnName.Contains("姓名"))
                return new DataGridLength(100);
            else if (columnName.Contains("住所") || columnName.Contains("地址"))
                return new DataGridLength(200);
            else if (columnName.Contains("电话") || columnName.Contains("联系"))
                return new DataGridLength(120);
            else if (columnName.Contains("日期") || columnName.Contains("年月日"))
                return new DataGridLength(100);
            else
                return new DataGridLength(120);
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            // 分页控件事件
            PaginationControl.PageChanged += PaginationControl_PageChanged;
            PaginationControl.PageSizeChanged += PaginationControl_PageSizeChanged;

            // DataGrid选择变化事件
            NationalDataGrid.SelectionChanged += NationalDataGrid_SelectionChanged;

            // 搜索框事件
            SearchTextBox.GotFocus += SearchTextBox_GotFocus;
            SearchTextBox.LostFocus += SearchTextBox_LostFocus;
            SearchTextBox.TextChanged += SearchTextBox_TextChanged;
        }

        /// <summary>
        /// 设置搜索框占位符效果
        /// </summary>
        private void SetupSearchBoxPlaceholder()
        {
            //SearchTextBox.Foreground = Brushes.Gray;
        }

        /// <summary>
        /// 搜索框获得焦点事件
        /// </summary>
        private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (SearchTextBox.Text == "受給者番号・事業者コード・氏名等のキーワードを入力")
            {
                SearchTextBox.Text = "";
                //SearchTextBox.Foreground = Brushes.Black;
            }
        }

        /// <summary>
        /// 搜索框失去焦点事件
        /// </summary>
        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                SearchTextBox.Text = "受給者番号・事業者コード・氏名等のキーワードを入力";
                //SearchTextBox.Foreground = Brushes.Gray;
            }
        }

        /// <summary>
        /// 搜索框文本变化事件
        /// </summary>
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // 延迟搜索，避免频繁查询
            if (_searchTimer != null)
            {
                _searchTimer.Stop();
            }
            _searchTimer = new System.Windows.Threading.DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(500)
            };
            _searchTimer.Tick += (s, args) =>
            {
                _searchTimer.Stop();
                _currentPage = 1; // 重置到第一页
                ApplyPagination();
            };
            _searchTimer.Start();
        }

        /// <summary>
        /// DataGrid选择变化事件
        /// </summary>
        private void NationalDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdateStatusInfo();
        }

        /// <summary>
        /// 应用分页
        /// </summary>
        private void ApplyPagination()
        {
            // 使用数据库分页查询
            Task.Run(async () => await LoadPagedDataAsync());
        }

        /// <summary>
        /// 更新分页信息
        /// </summary>
        private void UpdatePagination()
        {
            var totalPages = (int)Math.Ceiling((double)_totalRecords / _pageSize);

            PaginationControl.CurrentPage = _currentPage;
            PaginationControl.TotalPages = Math.Max(1, totalPages);
            PaginationControl.TotalRecords = _totalRecords;
            PaginationControl.PageSize = _pageSize;
        }

        /// <summary>
        /// 更新状态信息
        /// </summary>
        private void UpdateStatusInfo()
        {
            int selectedCount = NationalDataGrid.SelectedItems.Count;

            // 尝试更新主窗口的状态栏
            var mainWindow = Application.Current.MainWindow as MainWindow;
            mainWindow?.UpdateStatusBar(_totalRecords, selectedCount, PaginationControl.TotalPages, "");
        }

        /// <summary>
        /// 分页控件页码变化事件
        /// </summary>
        private void PaginationControl_PageChanged(object sender, PageChangedEventArgs e)
        {
            _currentPage = e.NewPage;
            ApplyPagination();
        }

        /// <summary>
        /// 分页控件页面大小变化事件
        /// </summary>
        private void PaginationControl_PageSizeChanged(object sender, PageSizeChangedEventArgs e)
        {
            _pageSize = e.NewPageSize;
            _currentPage = 1; // 重置到第一页
            ApplyPagination();
        }

        /// <summary>
        /// 新增按钮点击事件
        /// </summary>
        private async void AddButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var editWindow = new EditWindow("新規国保連データ追加", _columnNames);
                if (editWindow.ShowDialog() == true && editWindow.IsSaved)
                {
                    // 创建KokuhoRenData对象
                    var kokuhoRenData = new KokuhoRenData
                    {
                        サービス提供年月 = editWindow.ResultData.GetValueOrDefault("サービス提供年月", "").ToString(),
                        請求年月日 = editWindow.ResultData.GetValueOrDefault("請求年月日", "").ToString(),
                        請求回数 = editWindow.ResultData.GetValueOrDefault("請求回数", "").ToString(),
                        審査年月 = editWindow.ResultData.GetValueOrDefault("審査年月", "").ToString(),
                        事業者コード = editWindow.ResultData.GetValueOrDefault("事業者コード", "").ToString(),
                        事業者名称 = editWindow.ResultData.GetValueOrDefault("事業者名称", "").ToString(),
                        受給者番号 = editWindow.ResultData.GetValueOrDefault("受給者番号", "").ToString(),
                        受給者名称 = editWindow.ResultData.GetValueOrDefault("受給者名称カナ", "").ToString(),
                        受給者名称カナ = editWindow.ResultData.GetValueOrDefault("受給者名称カナ", "0").ToString(),
                        児童名称 = editWindow.ResultData.GetValueOrDefault("児童名称", "0").ToString(),
                        児童名称カナ = editWindow.ResultData.GetValueOrDefault("児童名称カナ", "").ToString(),
                        身体 = editWindow.ResultData.GetValueOrDefault("身体", "").ToString(),
                        知的 = editWindow.ResultData.GetValueOrDefault("知的", "").ToString(),
                        精神 = editWindow.ResultData.GetValueOrDefault("精神", "").ToString(),
                        難病 = editWindow.ResultData.GetValueOrDefault("難病", "").ToString(),
                        単価障害程度区分 = editWindow.ResultData.GetValueOrDefault("単価障害程度区分", "").ToString(),
                        障害支援区分 = editWindow.ResultData.GetValueOrDefault("障害支援区分", "").ToString(),
                        サービスコード = editWindow.ResultData.GetValueOrDefault("サービスコード", "").ToString(),
                        サービス名称 = editWindow.ResultData.GetValueOrDefault("サービス名称", "").ToString(),
                        算定時間 = editWindow.ResultData.GetValueOrDefault("算定時間", "0").ToString(),
                        回数 = editWindow.ResultData.GetValueOrDefault("回数", "0").ToString(),
                        算定時間x回数 = editWindow.ResultData.GetValueOrDefault("算定時間x回数", "0").ToString(),
                        単位数 = editWindow.ResultData.GetValueOrDefault("単位数", "0").ToString(),
                        サービス単位 = editWindow.ResultData.GetValueOrDefault("サービス単位", "").ToString(),
                        連合会審査区分名称 = editWindow.ResultData.GetValueOrDefault("連合会審査区分名称", "").ToString(),
                        審査区分名称 = editWindow.ResultData.GetValueOrDefault("審査区分名称", "").ToString(),
                        返戻事由名称 = editWindow.ResultData.GetValueOrDefault("返戻事由名称", "").ToString(),
                        判定フラグ = editWindow.ResultData.GetValueOrDefault("判定フラグ", "").ToString(),
                        status = editWindow.ResultData.GetValueOrDefault("status", "").ToString()
                    };

                    // 保存到数据库
                    await _kokuhoRenRepository.CreateKokuhoRenDataAsync(kokuhoRenData);

                    // 刷新数据
                    LoadDataFromDatabase();
                    MessageBox.Show("データの追加が完了しました！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"データ追加に失敗しました：{ex.Message}", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 编辑按钮点击事件
        /// </summary>
        private async void EditButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var selectedItem = button?.Tag as NationalData;

            //var selectedItem = NationalDataGrid.SelectedItem as NationalData;
            if (selectedItem == null)
            {
                MessageBox.Show("編集するレコードを選択してください！", "お知らせ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }
            var editData = new Dictionary<string, object>();
            foreach (var columnName in _columnNames)
            {
                editData[columnName] = selectedItem.GetProperty(columnName);
            }

            try
            {
                var editWindow = new EditWindow("国保連データ編集", _columnNames, editData);
                if (editWindow.ShowDialog() == true && editWindow.IsSaved)
                {
                    // 获取No值用于更新
                    var noValue = selectedItem.GetProperty("No");
                    if (int.TryParse(noValue, out int no))
                    {
                        // 创建更新的KokuhoRenData对象
                        var kokuhoRenData = new KokuhoRenData
                        {
                            No = no,
                            サービス提供年月 = editWindow.ResultData.GetValueOrDefault("サービス提供年月", "").ToString(),
                            請求年月日 = editWindow.ResultData.GetValueOrDefault("請求年月日", "").ToString(),
                            請求回数 = editWindow.ResultData.GetValueOrDefault("請求回数", "").ToString(),
                            審査年月 = editWindow.ResultData.GetValueOrDefault("審査年月", "").ToString(),
                            事業者コード = editWindow.ResultData.GetValueOrDefault("事業者コード", "").ToString(),
                            事業者名称 = editWindow.ResultData.GetValueOrDefault("事業者名称", "").ToString(),
                            受給者番号 = editWindow.ResultData.GetValueOrDefault("受給者番号", "").ToString(),
                            受給者名称 = editWindow.ResultData.GetValueOrDefault("受給者名称カナ", "").ToString(),
                            受給者名称カナ = editWindow.ResultData.GetValueOrDefault("受給者名称カナ", "0").ToString(),
                            児童名称 = editWindow.ResultData.GetValueOrDefault("児童名称", "0").ToString(),
                            児童名称カナ = editWindow.ResultData.GetValueOrDefault("児童名称カナ", "").ToString(),
                            身体 = editWindow.ResultData.GetValueOrDefault("身体", "").ToString(),
                            知的 = editWindow.ResultData.GetValueOrDefault("知的", "").ToString(),
                            精神 = editWindow.ResultData.GetValueOrDefault("精神", "").ToString(),
                            難病 = editWindow.ResultData.GetValueOrDefault("難病", "").ToString(),
                            単価障害程度区分 = editWindow.ResultData.GetValueOrDefault("単価障害程度区分", "").ToString(),
                            障害支援区分 = editWindow.ResultData.GetValueOrDefault("障害支援区分", "").ToString(),
                            サービスコード = editWindow.ResultData.GetValueOrDefault("サービスコード", "").ToString(),
                            サービス名称 = editWindow.ResultData.GetValueOrDefault("サービス名称", "").ToString(),
                            算定時間 = editWindow.ResultData.GetValueOrDefault("算定時間", "0").ToString(),
                            回数 = editWindow.ResultData.GetValueOrDefault("回数", "0").ToString(),
                            算定時間x回数 = editWindow.ResultData.GetValueOrDefault("算定時間x回数", "0").ToString(),
                            単位数 = editWindow.ResultData.GetValueOrDefault("単位数", "0").ToString(),
                            サービス単位 = editWindow.ResultData.GetValueOrDefault("サービス単位", "").ToString(),
                            連合会審査区分名称 = editWindow.ResultData.GetValueOrDefault("連合会審査区分名称", "").ToString(),
                            審査区分名称 = editWindow.ResultData.GetValueOrDefault("審査区分名称", "").ToString(),
                            返戻事由名称 = editWindow.ResultData.GetValueOrDefault("返戻事由名称", "").ToString(),
                            判定フラグ = editWindow.ResultData.GetValueOrDefault("判定フラグ", "").ToString(),
                            status = editWindow.ResultData.GetValueOrDefault("status", "").ToString()
                        };

                        // 更新数据库
                        await _kokuhoRenRepository.UpdateKokuhoRenDataAsync(kokuhoRenData);

                        // 刷新数据
                        LoadDataFromDatabase();
                        MessageBox.Show("データの更新が完了しました！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("レコードIDを取得できません！", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"データ編集に失敗しました：{ex.Message}", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 删除按钮点击事件
        /// </summary>
        private async void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedItem = NationalDataGrid.SelectedItem as NationalData;
            if (selectedItem == null)
            {
                MessageBox.Show("削除するレコードを選択してください！", "お知らせ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var recipientName = selectedItem.GetProperty("受給者番号") ?? "不明";
            var result = MessageBox.Show($"受給者「{recipientName}」の国保連データを削除しますか？", "削除確認",
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    // 获取No值用于删除
                    var noValue = selectedItem.GetProperty("No");
                    if (int.TryParse(noValue, out int no))
                    {
                        // 从数据库删除
                        await _kokuhoRenRepository.DeleteAsync("\"No\" = @no",
                            _kokuhoRenRepository.CreateParameter("@no", no));

                        // 刷新数据
                        LoadDataFromDatabase();
                        MessageBox.Show("データの削除が完了しました！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("レコードIDを取得できません！", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"データ削除に失敗しました：{ex.Message}", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 搜索按钮点击事件
        /// </summary>
        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            _currentPage = 1;
            ApplyPagination();
        }

        /// <summary>
        /// 导出按钮点击事件
        /// </summary>
        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("エクスポート機能は開発中です...", "お知らせ", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 打印按钮点击事件
        /// </summary>
        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("印刷機能は開発中です...", "お知らせ", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 刷新按钮点击事件
        /// </summary>
        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                InitializeData();
                MessageBox.Show("データの更新が完了しました！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"データ更新に失敗しました：{ex.Message}", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _databaseManager?.Dispose();
        }
    }
}
